#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Token Pusher - 最终简化版GUI
作者: kkk
邮箱: <EMAIL>
"""

import tkinter as tk
from tkinter import messagebox, scrolledtext, filedialog
import threading
import sys
import os

# 导入核心功能
from cursor_token_pusher import CursorTokenPusher

class CursorTokenPusherGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Cursor Token Pusher v2.0")
        self.root.geometry("700x600")
        self.root.configure(bg="#f8fafc")
        
        # 设置图标
        try:
            self.root.iconbitmap("cursor.ico")
        except:
            pass
        
        self.pusher = CursorTokenPusher()
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_frame = tk.Frame(self.root, bg="#f8fafc")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, 
                              text="🚀 Cursor Token Pusher",
                              font=("Arial", 18, "bold"),
                              fg="#1f2937",
                              bg="#f8fafc")
        title_label.pack(pady=(0, 5))
        
        subtitle_label = tk.Label(main_frame,
                                 text="现代化Token推送工具",
                                 font=("Arial", 10),
                                 fg="#6b7280",
                                 bg="#f8fafc")
        subtitle_label.pack(pady=(0, 20))
        
        # 状态卡片
        self.create_status_card(main_frame)
        
        # Token输入卡片
        self.create_token_card(main_frame)
        
        # Cursor路径卡片
        self.create_path_card(main_frame)
        
        # 按钮区域
        self.create_buttons(main_frame)
        
        # 日志区域
        self.create_log_area(main_frame)
        
        # 检查状态
        self.check_status()
    
    def create_status_card(self, parent):
        """创建状态卡片"""
        card = tk.Frame(parent, bg="white", relief="solid", bd=1)
        card.pack(fill=tk.X, pady=(0, 15))
        
        content = tk.Frame(card, bg="white")
        content.pack(fill=tk.X, padx=15, pady=15)
        
        tk.Label(content, text="📊 Cursor 状态", font=("Arial", 12, "bold"), 
                fg="#1f2937", bg="white").pack(anchor=tk.W)
        
        status_frame = tk.Frame(content, bg="white")
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 数据库状态
        db_frame = tk.Frame(status_frame, bg="white")
        db_frame.pack(fill=tk.X, pady=(0, 5))
        tk.Label(db_frame, text="数据库:", font=("Arial", 10), fg="#1f2937", bg="white").pack(side=tk.LEFT)
        self.db_status = tk.Label(db_frame, text="检查中...", font=("Arial", 10), fg="#6b7280", bg="white")
        self.db_status.pack(side=tk.LEFT, padx=(10, 0))
        
        # 可执行文件状态
        exe_frame = tk.Frame(status_frame, bg="white")
        exe_frame.pack(fill=tk.X)
        tk.Label(exe_frame, text="可执行文件:", font=("Arial", 10), fg="#1f2937", bg="white").pack(side=tk.LEFT)
        self.exe_status = tk.Label(exe_frame, text="检查中...", font=("Arial", 10), fg="#6b7280", bg="white")
        self.exe_status.pack(side=tk.LEFT, padx=(10, 0))
    
    def create_token_card(self, parent):
        """创建Token输入卡片"""
        card = tk.Frame(parent, bg="white", relief="solid", bd=1)
        card.pack(fill=tk.X, pady=(0, 15))
        
        content = tk.Frame(card, bg="white")
        content.pack(fill=tk.X, padx=15, pady=15)
        
        tk.Label(content, text="🔑 Token 信息", font=("Arial", 12, "bold"), 
                fg="#1f2937", bg="white").pack(anchor=tk.W)
        
        warning = tk.Label(content, text="⚠️ 推送前请先：1) 退出Cursor账号  2) 完全关闭Cursor",
                          font=("Arial", 9), fg="#f59e0b", bg="white")
        warning.pack(anchor=tk.W, pady=(5, 10))
        
        tk.Label(content, text="WorkosCursorSessionToken:", font=("Arial", 10), 
                fg="#1f2937", bg="white").pack(anchor=tk.W)
        
        token_frame = tk.Frame(content, bg="white")
        token_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.token_entry = tk.Entry(token_frame, font=("Arial", 10), show="*", relief="solid", bd=1)
        self.token_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=5)
        
        self.show_var = tk.BooleanVar()
        show_btn = tk.Checkbutton(token_frame, text="👁", variable=self.show_var,
                                 command=self.toggle_token, bg="white", relief="flat")
        show_btn.pack(side=tk.RIGHT, padx=(5, 0))
    
    def create_path_card(self, parent):
        """创建路径配置卡片"""
        card = tk.Frame(parent, bg="white", relief="solid", bd=1)
        card.pack(fill=tk.X, pady=(0, 15))
        
        content = tk.Frame(card, bg="white")
        content.pack(fill=tk.X, padx=15, pady=15)
        
        tk.Label(content, text="📁 Cursor 路径配置", font=("Arial", 12, "bold"), 
                fg="#1f2937", bg="white").pack(anchor=tk.W)
        
        tk.Label(content, text="请指定Cursor可执行文件路径（例如：D:\\cursor\\Cursor.exe）",
                font=("Arial", 9), fg="#6b7280", bg="white").pack(anchor=tk.W, pady=(5, 10))
        
        path_frame = tk.Frame(content, bg="white")
        path_frame.pack(fill=tk.X)
        
        self.path_entry = tk.Entry(path_frame, font=("Arial", 10), relief="solid", bd=1)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=5)
        
        browse_btn = tk.Button(path_frame, text="📂 浏览", command=self.browse_path,
                              font=("Arial", 9), padx=10, pady=5)
        browse_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        detect_btn = tk.Button(path_frame, text="🔍 自动检测", command=self.auto_detect,
                              font=("Arial", 9), bg="#2563eb", fg="white", padx=10, pady=5)
        detect_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 预填充路径
        if self.pusher.cursor_exe_path:
            self.path_entry.insert(0, self.pusher.cursor_exe_path)
    
    def create_buttons(self, parent):
        """创建按钮区域"""
        button_frame = tk.Frame(parent, bg="#f8fafc")
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 主按钮
        self.push_button = tk.Button(button_frame,
                                   text="🚀 推送 Token",
                                   command=self.push_token,
                                   font=("Arial", 12, "bold"),
                                   bg="#2563eb",
                                   fg="white",
                                   relief="flat",
                                   padx=30,
                                   pady=12,
                                   cursor="hand2")
        self.push_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 次要按钮
        backup_btn = tk.Button(button_frame,
                              text="💾 备份数据库",
                              command=self.backup_db,
                              font=("Arial", 10),
                              padx=20,
                              pady=8)
        backup_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        test_btn = tk.Button(button_frame,
                            text="🔍 测试连接",
                            command=self.test_connection,
                            font=("Arial", 10),
                            padx=20,
                            pady=8)
        test_btn.pack(side=tk.LEFT)
    
    def create_log_area(self, parent):
        """创建日志区域"""
        card = tk.Frame(parent, bg="white", relief="solid", bd=1)
        card.pack(fill=tk.BOTH, expand=True)
        
        content = tk.Frame(card, bg="white")
        content.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        tk.Label(content, text="📋 操作日志", font=("Arial", 12, "bold"), 
                fg="#1f2937", bg="white").pack(anchor=tk.W)
        
        self.log_text = scrolledtext.ScrolledText(content, height=8, font=("Consolas", 9),
                                                bg="#1e1e1e", fg="#d4d4d4", relief="solid", bd=1)
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 添加欢迎消息
        self.log("🎉 欢迎使用 Cursor Token Pusher v2.0!")
        self.log("⚠️ 重要提示：推送前请先退出Cursor账号并完全关闭Cursor!")
        self.log("💡 输入您的 WorkosCursorSessionToken 开始使用")
    
    def log(self, message):
        """添加日志"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def toggle_token(self):
        """切换Token显示"""
        if self.show_var.get():
            self.token_entry.config(show="")
        else:
            self.token_entry.config(show="*")
    
    def browse_path(self):
        """浏览路径"""
        filename = filedialog.askopenfilename(
            title="选择Cursor.exe文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.path_entry.delete(0, tk.END)
            self.path_entry.insert(0, filename)
            self.log(f"📁 已选择路径: {filename}")
    
    def auto_detect(self):
        """自动检测路径"""
        def detect():
            self.log("🔍 正在自动检测Cursor路径...")
            from cursor_token_pusher import CursorTokenPusher
            temp = CursorTokenPusher()
            if temp.cursor_exe_path:
                self.path_entry.delete(0, tk.END)
                self.path_entry.insert(0, temp.cursor_exe_path)
                self.log(f"✅ 检测成功: {temp.cursor_exe_path}")
                self.pusher.cursor_exe_path = temp.cursor_exe_path
            else:
                self.log("❌ 自动检测失败，请手动选择")
        threading.Thread(target=detect, daemon=True).start()
    
    def check_status(self):
        """检查状态"""
        def check():
            if self.pusher.check_cursor_installation():
                self.db_status.config(text="✅ 已找到", fg="#10b981")
                self.log("✅ Cursor数据库检测成功")
            else:
                self.db_status.config(text="❌ 未找到", fg="#ef4444")
                self.log("❌ Cursor数据库未找到")
            
            if self.pusher.cursor_exe_path:
                self.exe_status.config(text="✅ 已找到", fg="#10b981")
                self.log(f"✅ Cursor可执行文件: {self.pusher.cursor_exe_path}")
            else:
                self.exe_status.config(text="❌ 未找到", fg="#ef4444")
                self.log("❌ Cursor可执行文件未找到")
        threading.Thread(target=check, daemon=True).start()
    
    def backup_db(self):
        """备份数据库"""
        def backup():
            self.log("🔄 正在备份数据库...")
            if self.pusher.backup_database():
                self.log("✅ 数据库备份成功")
                messagebox.showinfo("成功", "数据库备份成功！")
            else:
                self.log("❌ 数据库备份失败")
                messagebox.showerror("错误", "数据库备份失败！")
        threading.Thread(target=backup, daemon=True).start()
    
    def test_connection(self):
        """测试连接"""
        def test():
            self.log("🔍 正在测试连接...")
            if self.pusher.verify_token_installation():
                self.log("✅ 连接正常")
                messagebox.showinfo("测试结果", "Cursor连接正常！")
            else:
                self.log("⚠️ 连接异常或未配置Token")
                messagebox.showwarning("测试结果", "连接异常或未配置Token")
        threading.Thread(target=test, daemon=True).start()
    
    def push_token(self):
        """推送Token"""
        token = self.token_entry.get().strip()
        cursor_path = self.path_entry.get().strip()
        
        if not token:
            self.log("❌ 请输入Token")
            messagebox.showerror("错误", "请输入WorkosCursorSessionToken！")
            return
        
        # 确认操作
        if not messagebox.askyesno("确认推送", 
                                  "⚠️ 推送前确认：\n\n1. 是否已退出Cursor账号？\n2. 是否已完全关闭Cursor？\n\n确认继续？"):
            return
        
        # 更新路径
        if cursor_path and os.path.exists(cursor_path):
            self.pusher.cursor_exe_path = cursor_path
        
        # 禁用按钮
        self.push_button.config(state="disabled", text="推送中...")
        
        def push():
            try:
                self.log("🚀 开始推送Token...")
                self.log(f"🔑 Token: {token[:20]}...")
                
                if self.pusher.push_token(token):
                    self.log("✅ Token推送成功！")
                    
                    if self.pusher.verify_token_installation():
                        self.log("✅ Token验证通过")
                        
                        if messagebox.askyesno("推送成功", "Token推送成功！\n\n是否立即启动Cursor？"):
                            self.log("🚀 正在启动Cursor...")
                            if self.pusher.launch_cursor():
                                self.log("✅ Cursor启动成功！")
                                messagebox.showinfo("完成", "Cursor已启动，认证信息已生效！")
                            else:
                                self.log("❌ Cursor启动失败")
                                messagebox.showwarning("提示", "启动失败，请手动启动Cursor")
                        else:
                            messagebox.showinfo("完成", "Token推送完成！请重启Cursor生效。")
                    else:
                        self.log("⚠️ Token验证失败")
                        messagebox.showwarning("警告", "Token推送可能不完整")
                else:
                    self.log("❌ Token推送失败")
                    messagebox.showerror("错误", "Token推送失败！")
                    
            except Exception as e:
                self.log(f"❌ 错误: {str(e)}")
                messagebox.showerror("错误", f"发生错误：{str(e)}")
            finally:
                self.push_button.config(state="normal", text="🚀 推送 Token")
        
        threading.Thread(target=push, daemon=True).start()

def main():
    root = tk.Tk()
    app = CursorTokenPusherGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
